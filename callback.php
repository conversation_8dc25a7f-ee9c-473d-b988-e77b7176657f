<?php
// Callback handler for payment processing

// تنظیمات ربات تلگرام
$botToken = "7626326794:AAHKrt-VqqMbv72cJzbLIFMGQK-nE647fiE";

/**
 * Function to send Telegram message
 */
function sendTelegramMessage($chat_id, $text, $reply_markup = null) {
    global $botToken;
    $url = "https://api.telegram.org/bot{$botToken}/sendMessage";

    $data = [
        'chat_id' => $chat_id,
        'text' => $text,
        'parse_mode' => 'HTML'
    ];

    if ($reply_markup) {
        $data['reply_markup'] = $reply_markup;
    }

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    $response = curl_exec($ch);
    curl_close($ch);

    return json_decode($response);
}

/**
 * Function to get user token from user file
 */
function getUserToken($user_id) {
    $userFile = "data/user/{$user_id}.json";
    if (file_exists($userFile)) {
        $userData = json_decode(file_get_contents($userFile), true);
        return isset($userData['userfild']['user_token']) ? $userData['userfild']['user_token'] : null;
    }
    return null;
}

/**
 * Function to save voucher to database
 */
function saveVoucher($voucher_code, $user_id, $user_token, $amount, $trackId) {
    // ایجاد پوشه vouchers در صورت عدم وجود
    if (!file_exists("data/vouchers")) {
        mkdir("data/vouchers", 0777, true);
    }

    $voucherData = [
        'voucher_code' => $voucher_code,
        'creator_user_id' => $user_id,
        'creator_token' => $user_token,
        'amount_toman' => $amount,
        'track_id' => $trackId,
        'created_at' => date('Y-m-d H:i:s'),
        'created_timestamp' => time(),
        'is_used' => false,
        'used_by' => null,
        'used_at' => null,
        'status' => 'active'
    ];

    // ذخیره در فایل جداگانه برای هر ووچر
    $voucherFile = "data/vouchers/{$voucher_code}.json";
    file_put_contents($voucherFile, json_encode($voucherData, JSON_PRETTY_PRINT));

    // ذخیره در فایل لیست کلی ووچرها
    $allVouchersFile = "data/vouchers/all_vouchers.json";
    $allVouchers = [];
    if (file_exists($allVouchersFile)) {
        $allVouchers = json_decode(file_get_contents($allVouchersFile), true);
    }

    $allVouchers[] = [
        'voucher_code' => $voucher_code,
        'creator_user_id' => $user_id,
        'amount_toman' => $amount,
        'created_at' => date('Y-m-d H:i:s'),
        'status' => 'active'
    ];

    file_put_contents($allVouchersFile, json_encode($allVouchers, JSON_PRETTY_PRINT));

    return true;
}

/**
 * Function to connect to Zibal API for verification
 */
function postToZibal($path, $parameters) {
    $url = 'https://gateway.zibal.ir/v1/' . $path;

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($parameters));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    curl_close($ch);

    return json_decode($response);
}

// بررسی پارامترهای ضروری
if (isset($_GET['trackId']) && isset($_GET['orderId'])) {
    $trackId = $_GET['trackId'];
    $orderId = $_GET['orderId'];
    $success = $_GET['success'] ?? '0';
    $status = $_GET['status'] ?? '0';

    // استخراج user_id از orderId (فرمت: BV-TIMESTAMP-USERID یا SPX-TIMESTAMP-USERID)
    $user_id = '0';
    if (preg_match('/BV-\d+-(\d+)/', $orderId, $matches)) {
        $user_id = $matches[1];
    } else if (preg_match('/SPX-\d+-(\d+)/', $orderId, $matches)) {
        $user_id = $matches[1];
    } else if (preg_match('/(\d+)$/', $orderId, $matches)) {
        // Fallback to old format
        $user_id = $matches[1];
    }

    echo "<h3>در حال بررسی پرداخت...</h3>";
    echo "<p>شناسه پیگیری: $trackId</p>";
    echo "<p>شماره سفارش: $orderId</p>";
    echo "<p>کاربر: $user_id</p>";

    // تأیید پرداخت با API درگاه Zibal
    $parameters = array(
        "merchant" => "zibal", // کلید مرچنت واقعی خود را قرار دهید
        "trackId" => $trackId
    );

    $response = postToZibal('verify', $parameters);

    if ($response) {
        $resultCode = $response->result ?? 'undefined';

        // بررسی کد 203 (تراکنش قبلاً تأیید شده)
        if ($resultCode == 203) {
            $message = $response->message ?? '';
            // اگر پیام "تایید شده" باشد، موفق در نظر بگیر
            if (strpos($message, 'تایید شده') !== false || strpos($message, 'verified') !== false) {
                $response->result = 100;
            }
        }

        // بررسی نتیجه تأیید (100 = موفق)
        if (isset($response->result) && $response->result == 100) {
            // پرداخت موفق تأیید شد

            // بارگیری اطلاعات سفارش از دیتابیس
            require_once 'config.php';
            require_once 'JsonDatabase.php';
            $db = new JsonDatabase($JSON_DB_DIR);
            $orderData = $db->load('orders', $orderId);

            // تعداد ووچر از اطلاعات سفارش (خط 58 bot.php)
            if (!$orderData) {
                error_log("Order data not found for orderId: " . $orderId);
                $voucher_count = 1; // Default fallback
            } else {
                $voucher_count = isset($orderData['voucher_count']) ? $orderData['voucher_count'] : 1;
            }



            $paid_amount = ($response->amount ?? 0) / 10; // مبلغ پرداختی (شامل کارمزد)

            // محاسبه ارزش ووچر (خط 59 - کسر کارمزد 7هزار تومان)
            $voucher_value = $paid_amount - 7000; // کسر کارمزد 7هزار تومان

            $payment_time = date('Y/m/d H:i:s');

            // دریافت توکن کاربر
            $user_token = getUserToken($user_id);
            if (!$user_token) {
                $user_token = "نامشخص";
            }

            // تولید یک کد ووچر با ارزش کل (بدون کارمزد)
            $part1 = strtoupper(substr(md5(time()), 0, 4));
            $part2 = strtoupper(substr(md5(rand()), 0, 4));
            $part3 = strtoupper(substr(md5(microtime()), 0, 4));
            $part4 = strtoupper(substr(md5($trackId), 0, 4));
            $voucher_code = "BV-$part1-$part2-$part3-$part4";

            // ذخیره ووچر در دیتابیس با ارزش کل
            saveVoucher($voucher_code, $user_id, $user_token, $voucher_value, $trackId);

            // ذخیره تراکنش خرید در دیتابیس
            $transactionId = 'TXN-' . time() . '-' . $user_id;
            $db->saveTransaction($transactionId, [
                'user_id' => $user_id,
                'voucher_code' => $voucher_code,
                'amount' => $voucher_value,
                'currency' => 'IRR',
                'type' => 'voucher_purchase',
                'payment_method' => 'card',
                'status' => 'completed',
                'created_at' => time()
            ]);

            // ارسال پیام موفقیت به کاربر
            if ($user_id && $user_id != '0') {
                // Create keyboard with history button
                $success_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '📊 تاریخچه تراکنش‌ها',
                                'callback_data' => 'transaction_history'
                            ]
                        ]
                    ]
                ]);

                sendTelegramMessage($user_id,
                    "✅ پرداخت موفق!\n\n✱ زمان : $payment_time\n✱ مبلغ : " . number_format($paid_amount) . " تومان\n✱ شناسه پیگیری : <code>$trackId</code>\n\n🎟 کد ووچر شما : <code>$voucher_code</code>\n✱ ارزش ووچر : " . number_format($voucher_value) . " تومان\n✱ کد دریافتی را در جای امنی ذخیره کنید.\n✱ باتشکر از پرداخت شما.",
                    $success_keyboard
                );
            }

            // ذخیره کد ووچر در session یا فایل برای نمایش در صفحه success
            session_start();
            $_SESSION['voucher_code'] = $voucher_code;
            $_SESSION['payment_amount'] = $paid_amount;
            $_SESSION['payment_date'] = $payment_time;

            // Redirect به صفحه موفقیت
            header('Location: https://speedx-team.ir/Payment/success.php');
            exit;

        } else {
            // پرداخت ناموفق - ارسال پیام به کاربر
            if ($user_id && $user_id != '0') {
                $reply_markup = json_encode([
                    'inline_keyboard' => [
                        [
                            ['text' => '🔄 تلاش مجدد', 'callback_data' => 'buy_voucher']
                        ]
                    ]
                ]);

                sendTelegramMessage($user_id,
                    "❌ پرداخت ناموفق\n\nمتأسفانه پرداخت شما تأیید نشد.\n\n✱ کد خطا: <code>$resultCode</code>\n✱ در صورت کسر وجه از حساب شما، مبلغ ظرف 72 ساعت بازگردانده خواهد شد.\n\n🔄 برای تلاش مجدد، دکمه زیر را بزنید.",
                    $reply_markup
                );
            }

            // Redirect به صفحه خطا با کد خطا
            header('Location: https://speedx-team.ir/Payment/error.php?error=verification_failed&code=' . $resultCode);
            exit;
        }
    } else {
        // خطا در ارتباط با API
        if ($user_id && $user_id != '0') {
            sendTelegramMessage($user_id,
                "❌ خطا در ارتباط با درگاه پرداخت!\n\nمتاسفانه امکان بررسی وضعیت پرداخت شما وجود ندارد. اگر مبلغی از حساب شما کسر شده، لطفا با پشتیبانی تماس بگیرید."
            );
        }

        header('Location: https://speedx-team.ir/Payment/error.php?error=api_error');
        exit;
    }

} else {
    // پارامترهای ضروری موجود نیست
    // سعی در استخراج user_id از orderId اگر موجود باشد
    if (isset($_GET['orderId'])) {
        $orderId = $_GET['orderId'];
        $user_id = '0';

        if (preg_match('/BV-\d+-(\d+)/', $orderId, $matches)) {
            $user_id = $matches[1];
        } else if (preg_match('/SPX-\d+-(\d+)/', $orderId, $matches)) {
            $user_id = $matches[1];
        } else if (preg_match('/(\d+)$/', $orderId, $matches)) {
            $user_id = $matches[1];
        }

        if ($user_id && $user_id != '0') {
            $reply_markup = json_encode([
                'inline_keyboard' => [
                    [
                        ['text' => '🔄 تلاش مجدد', 'callback_data' => 'buy_voucher']
                    ]
                ]
            ]);

            sendTelegramMessage($user_id,
                "❌ پرداخت ناموفق!\n\nمتاسفانه پرداخت شما با موفقیت انجام نشد. می‌توانید مجدداً تلاش کنید یا با پشتیبانی تماس بگیرید.",
                $reply_markup
            );
        }
    }

    header('Location: https://speedx-team.ir/Payment/error.php?error=missing_parameters');
    exit;
}
?>
